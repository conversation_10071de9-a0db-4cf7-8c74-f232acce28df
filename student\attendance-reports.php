<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Attendance Reports';
$student_id = $_SESSION['user_id'];

// Get student's classes and attendance data
try {
    // Get student's enrolled classes
    $stmt = $db->prepare("
        SELECT DISTINCT
            c.id as class_id,
            c.section,
            co.name as course_name,
            co.code as course_code,
            u.full_name as teacher_name
        FROM enrollments ce
        JOIN classes c ON ce.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON c.teacher_id = u.id
        WHERE ce.student_id = ?
        ORDER BY co.name, c.section
    ");
    $stmt->execute([$student_id]);
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get attendance summary for each class
    $attendance_summary = [];
    foreach ($classes as $class) {
        $stmt = $db->prepare("
            SELECT
                COUNT(DISTINCT ats.id) as total_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'present' THEN ats.id END) as present_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'absent' THEN ats.id END) as absent_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'late' THEN ats.id END) as late_sessions
            FROM attendance_sessions ats
            LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = ?
            WHERE ats.class_id = ?
        ");
        $stmt->execute([$student_id, $class['class_id']]);
        $summary = $stmt->fetch(PDO::FETCH_ASSOC);

        $attendance_percentage = $summary['total_sessions'] > 0
            ? round(($summary['present_sessions'] / $summary['total_sessions']) * 100, 2)
            : 0;

        $attendance_summary[$class['class_id']] = array_merge($summary, [
            'attendance_percentage' => $attendance_percentage
        ]);
    }

    // Get recent attendance records
    $stmt = $db->prepare("
        SELECT
            ats.date,
            ats.start_time,
            ats.end_time,
            ar.status,
            ar.marked_at,
            c.section,
            co.name as course_name,
            co.code as course_code
        FROM attendance_records ar
        JOIN attendance_sessions ats ON ar.session_id = ats.id
        JOIN classes c ON ats.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        WHERE ar.student_id = ?
        ORDER BY ats.date DESC, ats.start_time DESC
        LIMIT 20
    ");
    $stmt->execute([$student_id]);
    $recent_attendance = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-chart-bar mr-3 text-indigo-600"></i>
                    Attendance Reports
                </h1>
                <p class="mt-2 text-gray-600">View your attendance statistics and records</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <?php
        $total_sessions = array_sum(array_column($attendance_summary, 'total_sessions'));
        $total_present = array_sum(array_column($attendance_summary, 'present_sessions'));
        $total_absent = array_sum(array_column($attendance_summary, 'absent_sessions'));
        $total_late = array_sum(array_column($attendance_summary, 'late_sessions'));
        $overall_percentage = $total_sessions > 0 ? round(($total_present / $total_sessions) * 100, 2) : 0;
        ?>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-percentage text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Overall Attendance</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $overall_percentage; ?>%</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Present</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_present; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-times text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Absent</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_absent; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Late</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_late; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Class-wise Attendance -->
    <div class="bg-white shadow-lg rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-chalkboard mr-2 text-indigo-600"></i>
                Class-wise Attendance
            </h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Sessions</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Late</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance %</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($classes as $class): ?>
                        <?php $summary = $attendance_summary[$class['class_id']]; ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($class['course_name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($class['course_code']); ?></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($class['section']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($class['teacher_name']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo $summary['total_sessions']; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <?php echo $summary['present_sessions']; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    <?php echo $summary['absent_sessions']; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                    <?php echo $summary['late_sessions']; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <?php
                                $percentage = $summary['attendance_percentage'];
                                $color_class = $percentage >= 75 ? 'text-green-600' : ($percentage >= 50 ? 'text-yellow-600' : 'text-red-600');
                                ?>
                                <span class="text-sm font-medium <?php echo $color_class; ?>">
                                    <?php echo $percentage; ?>%
                                </span>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Attendance Records -->
    <div class="bg-white shadow-lg rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-history mr-2 text-indigo-600"></i>
                Recent Attendance Records
            </h3>
        </div>
        <?php if (!empty($recent_attendance)): ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Marked At</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($recent_attendance as $record): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('M d, Y', strtotime($record['date'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($record['course_name']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($record['course_code']); ?></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo htmlspecialchars($record['section']); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('g:i A', strtotime($record['start_time'])) . ' - ' . date('g:i A', strtotime($record['end_time'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $status_colors = [
                                        'present' => 'bg-green-100 text-green-800',
                                        'absent' => 'bg-red-100 text-red-800',
                                        'late' => 'bg-yellow-100 text-yellow-800'
                                    ];
                                    $status_icons = [
                                        'present' => 'fas fa-check',
                                        'absent' => 'fas fa-times',
                                        'late' => 'fas fa-clock'
                                    ];
                                    ?>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_colors[$record['status']]; ?>">
                                        <i class="<?php echo $status_icons[$record['status']]; ?> mr-1"></i>
                                        <?php echo ucfirst($record['status']); ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?php echo $record['marked_at'] ? date('M d, Y g:i A', strtotime($record['marked_at'])) : 'Not marked'; ?>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-8">
                <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No attendance records found.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include '../includes/footer.php'; ?>