-- Fix for remaining database issues
-- This script will create missing tables and columns

USE attendance_system;

-- Create student_details table that the application expects
CREATE TABLE IF NOT EXISTS student_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    student_number VARCHAR(20) UNIQUE,
    department_id INT,
    admission_date DATE,
    graduation_date DATE,
    gpa DECIMAL(3,2),
    status ENUM('active', 'inactive', 'graduated', 'dropped') DEFAULT 'active',
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(15),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id)
);

-- Add student_id column to users table (this will be the student number/ID)
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS student_id VARCHAR(20) AFTER id,
ADD COLUMN IF NOT EXISTS emergency_contact VARCHAR(100) AFTER address,
ADD COLUMN IF NOT EXISTS emergency_phone VARCHAR(15) AFTER emergency_contact;

-- Update existing student users with student IDs
UPDATE users 
SET student_id = CONCAT('STU', LPAD(id, 4, '0'))
WHERE role = 'student' AND student_id IS NULL;

-- Insert student details for existing students
INSERT IGNORE INTO student_details (student_id, student_number, department_id, admission_date, status)
SELECT 
    u.id,
    u.student_id,
    1, -- Default to first department
    CURDATE(),
    'active'
FROM users u
WHERE u.role = 'student';

-- Fix any remaining issues with class schedule columns
ALTER TABLE classes 
MODIFY COLUMN schedule_day VARCHAR(20) DEFAULT 'Monday',
MODIFY COLUMN start_time TIME DEFAULT '09:00:00',
MODIFY COLUMN end_time TIME DEFAULT '10:30:00';

-- Update any NULL values in schedule columns
UPDATE classes 
SET 
    schedule_day = COALESCE(schedule_day, 'Monday'),
    start_time = COALESCE(start_time, '09:00:00'),
    end_time = COALESCE(end_time, '10:30:00')
WHERE schedule_day IS NULL OR start_time IS NULL OR end_time IS NULL;

-- Display success message
SELECT 'All database issues have been fixed!' as message;
SELECT 'student_details table created and users table updated.' as info;
