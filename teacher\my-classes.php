<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'My Classes';
$teacher_id = $_SESSION['user_id'];

// Initialize variables to prevent undefined variable errors
$classes = [];
$attendance_stats = [];
$recent_sessions = [];

try {
    // First, check which enrollment table exists
    $enrollment_table = 'enrollments';
    try {
        $db->query("SELECT 1 FROM enrollments LIMIT 1");
        $enrollment_table = 'enrollments';
    } catch (PDOException $e) {
        // class_enrollments doesn't exist, use enrollments
    }

    // Get teacher's classes
    $stmt = $db->prepare("
        SELECT
            c.id as class_id,
            c.section,
            c.schedule,
            c.room,
            co.id as course_id,
            co.name as course_name,
            co.code as course_code,
            co.credits,
            co.description as course_description,
            d.name as department_name,
            d.code as department_code,
            COUNT(ce.student_id) as enrolled_students
        FROM classes c
        JOIN courses co ON c.course_id = co.id
        JOIN departments d ON co.department_id = d.id
        LEFT JOIN $enrollment_table ce ON c.id = ce.class_id
        WHERE c.teacher_id = ?
        GROUP BY c.id, c.section, c.schedule, c.room,
                 co.id, co.name, co.code, co.credits, co.description, d.name, d.code
        ORDER BY co.name, c.section
    ");
    $stmt->execute([$teacher_id]);
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get attendance statistics for each class
    $attendance_stats = [];
    foreach ($classes as $class) {
        $stmt = $db->prepare("
            SELECT
                COUNT(DISTINCT ats.id) as total_sessions,
                COUNT(ar.id) as total_records,
                COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_count,
                COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_count,
                COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count
            FROM attendance_sessions ats
            LEFT JOIN attendance_records ar ON ats.id = ar.session_id
            WHERE ats.class_id = ?
        ");
        $stmt->execute([$class['class_id']]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        $attendance_percentage = $stats['total_records'] > 0
            ? round(($stats['present_count'] / $stats['total_records']) * 100, 2)
            : 0;

        $attendance_stats[$class['class_id']] = array_merge($stats, [
            'attendance_percentage' => $attendance_percentage
        ]);
    }

    // Get recent attendance sessions
    $stmt = $db->prepare("
        SELECT
            ats.id as session_id,
            ats.date,
            ats.start_time,
            ats.end_time,
            c.section,
            co.name as course_name,
            co.code as course_code,
            COUNT(ar.id) as records_count
        FROM attendance_sessions ats
        JOIN classes c ON ats.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id
        WHERE c.teacher_id = ?
        GROUP BY ats.id, ats.date, ats.start_time, ats.end_time, c.section, co.name, co.code
        ORDER BY ats.date DESC, ats.start_time DESC
        LIMIT 5
    ");
    $stmt->execute([$teacher_id]);
    $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-chalkboard mr-3 text-indigo-600"></i>
                    My Classes
                </h1>
                <p class="mt-2 text-gray-600">Manage your assigned classes and view student enrollment</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chalkboard text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($classes); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-graduate text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Students</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo array_sum(array_column($classes, 'enrolled_students')); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-calendar text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Sessions</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo array_sum(array_column($attendance_stats, 'total_sessions')); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Credits</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo array_sum(array_column($classes, 'credits')); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Classes List -->
    <?php if (!empty($classes)): ?>
        <div class="space-y-6 mb-8">
            <?php foreach ($classes as $class): ?>
                <?php $stats = $attendance_stats[$class['class_id']]; ?>
                <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($class['course_name']); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($class['course_code']); ?> - Section <?php echo htmlspecialchars($class['section']); ?></p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                    <?php echo $class['credits']; ?> Credits
                                </span>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    <?php echo $class['enrolled_students']; ?> Students
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                            <!-- Class Information -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Class Information</h4>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <i class="fas fa-building w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Department:</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($class['department_name']); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Schedule:</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($class['schedule'] ?: 'TBA'); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Room:</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($class['room'] ?: 'TBA'); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-users w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Enrolled:</span>
                                        <span class="ml-2"><?php echo $class['enrolled_students']; ?> students</span>
                                    </div>
                                </div>

                                <?php if (!empty($class['course_description'])): ?>
                                    <div class="mt-4">
                                        <h5 class="text-sm font-semibold text-gray-900 mb-2">Course Description</h5>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($class['course_description']); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Attendance Statistics -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Attendance Statistics</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Total Sessions:</span>
                                        <span class="text-sm font-medium text-gray-900"><?php echo $stats['total_sessions']; ?></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Present:</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <?php echo $stats['present_count']; ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Absent:</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <?php echo $stats['absent_count']; ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Late:</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <?php echo $stats['late_count']; ?>
                                        </span>
                                    </div>
                                    <div class="pt-2 border-t border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-gray-900">Avg Attendance:</span>
                                            <?php
                                            $percentage = $stats['attendance_percentage'];
                                            $color_class = $percentage >= 75 ? 'text-green-600' : ($percentage >= 50 ? 'text-yellow-600' : 'text-red-600');
                                            ?>
                                            <span class="text-sm font-bold <?php echo $color_class; ?>">
                                                <?php echo $percentage; ?>%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Quick Actions</h4>
                                <div class="space-y-2">
                                    <a href="mark-attendance.php?class_id=<?php echo $class['class_id']; ?>"
                                       class="w-full inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                        <i class="fas fa-plus-circle mr-2"></i>
                                        Mark Attendance
                                    </a>
                                    <a href="view-attendance.php?class_id=<?php echo $class['class_id']; ?>"
                                       class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <i class="fas fa-eye mr-2"></i>
                                        View Records
                                    </a>
                                    <a href="students.php?class_id=<?php echo $class['class_id']; ?>"
                                       class="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <i class="fas fa-users mr-2"></i>
                                        View Students
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="bg-white shadow-lg rounded-lg mb-8">
            <div class="text-center py-12">
                <i class="fas fa-chalkboard text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Classes Assigned</h3>
                <p class="text-gray-500">You don't have any classes assigned yet. Please contact the administrator for assistance.</p>
            </div>
        </div>
    <?php endif; ?>

    <!-- Recent Sessions -->
    <?php if (!empty($recent_sessions)): ?>
        <div class="bg-white shadow-lg rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-history mr-2 text-indigo-600"></i>
                    Recent Attendance Sessions
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Records</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($recent_sessions as $session): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('M d, Y', strtotime($session['date'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($session['course_name']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($session['course_code']); ?> - Section <?php echo htmlspecialchars($session['section']); ?></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('g:i A', strtotime($session['start_time'])) . ' - ' . date('g:i A', strtotime($session['end_time'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $session['records_count']; ?> records
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
