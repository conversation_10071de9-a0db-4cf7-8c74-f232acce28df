<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Attendance Reports';
$teacher_id = $_SESSION['user_id'];

// Get filter parameters
$selected_class = $_GET['class_id'] ?? '';
$date_from = $_GET['date_from'] ?? date('Y-m-01'); // First day of current month
$date_to = $_GET['date_to'] ?? date('Y-m-t'); // Last day of current month

try {
    // First, check which enrollment table exists
    $enrollment_table = 'enrollments';
    try {
        $db->query("SELECT 1 FROM class_enrollments LIMIT 1");
        $enrollment_table = 'class_enrollments';
    } catch (PDOException $e) {
        // class_enrollments doesn't exist, use enrollments
    }

    // Get teacher's classes
    $stmt = $db->prepare("
        SELECT
            c.id,
            c.section,
            co.name as course_name,
            co.code as course_code
        FROM classes c
        JOIN courses co ON c.course_id = co.id
        WHERE c.teacher_id = ?
        ORDER BY co.name, c.section
    ");
    $stmt->execute([$teacher_id]);
    $teacher_classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Build query for attendance data
    $where_conditions = ["c.teacher_id = ?"];
    $params = [$teacher_id];

    if ($selected_class) {
        $where_conditions[] = "c.id = ?";
        $params[] = $selected_class;
    }

    if ($date_from) {
        $where_conditions[] = "ats.date >= ?";
        $params[] = $date_from;
    }

    if ($date_to) {
        $where_conditions[] = "ats.date <= ?";
        $params[] = $date_to;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Get attendance sessions with statistics
    $stmt = $db->prepare("
        SELECT
            ats.id as session_id,
            ats.date,
            ats.start_time,
            ats.end_time,
            c.section,
            co.name as course_name,
            co.code as course_code,
            COUNT(ce.student_id) as total_students,
            COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as present_count,
            COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as absent_count,
            COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as late_count
        FROM attendance_sessions ats
        JOIN classes c ON ats.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        LEFT JOIN $enrollment_table ce ON c.id = ce.class_id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = ce.student_id
        WHERE $where_clause
        GROUP BY ats.id, ats.date, ats.start_time, ats.end_time, c.section, co.name, co.code
        ORDER BY ats.date DESC, ats.start_time DESC
    ");
    $stmt->execute($params);
    $attendance_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get overall statistics
    $stmt = $db->prepare("
        SELECT
            COUNT(DISTINCT ats.id) as total_sessions,
            COUNT(DISTINCT ce.student_id) as unique_students,
            COUNT(ar.id) as total_records,
            COUNT(CASE WHEN ar.status = 'present' THEN 1 END) as total_present,
            COUNT(CASE WHEN ar.status = 'absent' THEN 1 END) as total_absent,
            COUNT(CASE WHEN ar.status = 'late' THEN 1 END) as total_late
        FROM attendance_sessions ats
        JOIN classes c ON ats.class_id = c.id
        LEFT JOIN $enrollment_table ce ON c.id = ce.class_id
        LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = ce.student_id
        WHERE $where_clause
    ");
    $stmt->execute($params);
    $overall_stats = $stmt->fetch(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-chart-bar mr-3 text-indigo-600"></i>
                    Attendance Reports
                </h1>
                <p class="mt-2 text-gray-600">Generate and view detailed attendance reports for your classes</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Filters -->
    <div class="bg-white shadow-lg rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-filter mr-2 text-indigo-600"></i>
                Filter Reports
            </h3>
        </div>
        <div class="p-6">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="class_id" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                    <select id="class_id" name="class_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">All Classes</option>
                        <?php foreach ($teacher_classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>" <?php echo $selected_class == $class['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($class['course_name'] . ' - Section ' . $class['section']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-1">From Date</label>
                    <input type="date" id="date_from" name="date_from" value="<?php echo htmlspecialchars($date_from); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-1">To Date</label>
                    <input type="date" id="date_to" name="date_to" value="<?php echo htmlspecialchars($date_to); ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                </div>

                <div class="flex items-end">
                    <button type="submit" class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-calendar text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Sessions</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $overall_stats['total_sessions'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Present</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $overall_stats['total_present'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-times text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Absent</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $overall_stats['total_absent'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-clock text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Late</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $overall_stats['total_late'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Sessions -->
    <div class="bg-white shadow-lg rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-list mr-2 text-indigo-600"></i>
                Attendance Sessions
            </h3>
        </div>

        <?php if (!empty($attendance_sessions)): ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Students</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Late</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance %</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($attendance_sessions as $session): ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('M d, Y', strtotime($session['date'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($session['course_name']); ?></div>
                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($session['course_code']); ?> - Section <?php echo htmlspecialchars($session['section']); ?></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo date('g:i A', strtotime($session['start_time'])) . ' - ' . date('g:i A', strtotime($session['end_time'])); ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $session['total_students']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <?php echo $session['present_count']; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <?php echo $session['absent_count']; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <?php echo $session['late_count']; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $attendance_percentage = $session['total_students'] > 0
                                        ? round(($session['present_count'] / $session['total_students']) * 100, 1)
                                        : 0;
                                    $color_class = $attendance_percentage >= 75 ? 'text-green-600' : ($attendance_percentage >= 50 ? 'text-yellow-600' : 'text-red-600');
                                    ?>
                                    <span class="text-sm font-medium <?php echo $color_class; ?>">
                                        <?php echo $attendance_percentage; ?>%
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-8">
                <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                <p class="text-gray-500">No attendance sessions found for the selected criteria.</p>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
