<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a teacher
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'teacher') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Student List';
$teacher_id = $_SESSION['user_id'];

// Get filter parameters
$selected_class = $_GET['class_id'] ?? '';

// Initialize variables
$students = [];
$teacher_classes = [];
$attendance_stats = [];

try {
    // First, check which enrollment table exists
    $enrollment_table = 'enrollments';
    try {
        $db->query("SELECT 1 FROM enrollments LIMIT 1");
        $enrollment_table = 'enrollments';
    } catch (PDOException $e) {
        // class_enrollments doesn't exist, use enrollments
    }

    // Get teacher's classes for filter dropdown
    $stmt = $db->prepare("
        SELECT
            c.id,
            c.section,
            co.name as course_name,
            co.code as course_code
        FROM classes c
        JOIN courses co ON c.course_id = co.id
        WHERE c.teacher_id = ?
        ORDER BY co.name, c.section
    ");
    $stmt->execute([$teacher_id]);
    $teacher_classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Build query for students
    $where_conditions = ["c.teacher_id = ?"];
    $params = [$teacher_id];

    if ($selected_class) {
        $where_conditions[] = "c.id = ?";
        $params[] = $selected_class;
    }

    $where_clause = implode(' AND ', $where_conditions);

    // Get students enrolled in teacher's classes
    $stmt = $db->prepare("
        SELECT DISTINCT
            u.id as student_id,
            u.full_name as student_name,
            u.email as student_email,
            u.phone as student_phone,
            u.student_id as student_number,
            GROUP_CONCAT(
                CONCAT(co.name, ' (', co.code, ') - Section ', c.section)
                ORDER BY co.name SEPARATOR '; '
            ) as enrolled_courses
        FROM $enrollment_table ce
        JOIN classes c ON ce.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON ce.student_id = u.id
        WHERE $where_clause
        GROUP BY u.id, u.full_name, u.email, u.phone, u.student_id
        ORDER BY u.full_name
    ");
    $stmt->execute($params);
    $students = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get attendance statistics for each student
    $attendance_stats = [];
    foreach ($students as $student) {
        $stmt = $db->prepare("
            SELECT
                COUNT(DISTINCT ats.id) as total_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'present' THEN ats.id END) as present_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'absent' THEN ats.id END) as absent_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'late' THEN ats.id END) as late_sessions
            FROM attendance_sessions ats
            JOIN classes c ON ats.class_id = c.id
            LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = ?
            WHERE c.teacher_id = ?" . ($selected_class ? " AND c.id = ?" : "")
        );

        $query_params = [$student['student_id'], $teacher_id];
        if ($selected_class) {
            $query_params[] = $selected_class;
        }

        $stmt->execute($query_params);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);

        $attendance_percentage = $stats['total_sessions'] > 0
            ? round(($stats['present_sessions'] / $stats['total_sessions']) * 100, 2)
            : 0;

        $attendance_stats[$student['student_id']] = array_merge($stats, [
            'attendance_percentage' => $attendance_percentage
        ]);
    }

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
    // Ensure variables are initialized even on error
    $students = [];
    $teacher_classes = [];
    $attendance_stats = [];
}

include '../includes/header.php';
?>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-user-graduate mr-3 text-indigo-600"></i>
                    Student List
                </h1>
                <p class="mt-2 text-gray-600">View and manage students enrolled in your classes</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Filter -->
    <div class="bg-white shadow-lg rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-filter mr-2 text-indigo-600"></i>
                Filter Students
            </h3>
        </div>
        <div class="p-6">
            <form method="GET" class="flex items-end space-x-4">
                <div class="flex-1">
                    <label for="class_id" class="block text-sm font-medium text-gray-700 mb-1">Class</label>
                    <select id="class_id" name="class_id" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        <option value="">All Classes</option>
                        <?php foreach ($teacher_classes as $class): ?>
                            <option value="<?php echo $class['id']; ?>" <?php echo $selected_class == $class['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($class['course_name'] . ' - Section ' . $class['section']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Summary -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-users text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Students</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($students); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-percentage text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Avg Attendance</p>
                        <?php
                        $total_sessions = array_sum(array_column($attendance_stats, 'total_sessions'));
                        $total_present = array_sum(array_column($attendance_stats, 'present_sessions'));
                        $avg_attendance = $total_sessions > 0 ? round(($total_present / $total_sessions) * 100, 1) : 0;
                        ?>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $avg_attendance; ?>%</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chalkboard text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($teacher_classes); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Students List -->
    <?php if (!empty($students)): ?>
        <div class="bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">
                    <i class="fas fa-list mr-2 text-indigo-600"></i>
                    Students
                </h3>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enrolled Courses</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sessions</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Present</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Absent</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Late</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attendance %</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($students as $student): ?>
                            <?php $stats = $attendance_stats[$student['student_id']]; ?>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center mr-4">
                                            <i class="fas fa-user text-indigo-600"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($student['student_name']); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($student['student_number'] ?? 'N/A'); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <a href="mailto:<?php echo htmlspecialchars($student['student_email']); ?>" class="text-indigo-600 hover:text-indigo-800">
                                            <?php echo htmlspecialchars($student['student_email']); ?>
                                        </a>
                                    </div>
                                    <?php if (!empty($student['student_phone'])): ?>
                                        <div class="text-sm text-gray-500">
                                            <a href="tel:<?php echo htmlspecialchars($student['student_phone']); ?>" class="text-indigo-600 hover:text-indigo-800">
                                                <?php echo htmlspecialchars($student['student_phone']); ?>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">
                                        <?php
                                        $courses = explode('; ', $student['enrolled_courses']);
                                        foreach ($courses as $course):
                                        ?>
                                            <div class="mb-1">• <?php echo htmlspecialchars($course); ?></div>
                                        <?php endforeach; ?>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?php echo $stats['total_sessions']; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <?php echo $stats['present_sessions']; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        <?php echo $stats['absent_sessions']; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                        <?php echo $stats['late_sessions']; ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php
                                    $percentage = $stats['attendance_percentage'];
                                    $color_class = $percentage >= 75 ? 'text-green-600' : ($percentage >= 50 ? 'text-yellow-600' : 'text-red-600');
                                    ?>
                                    <span class="text-sm font-medium <?php echo $color_class; ?>">
                                        <?php echo $percentage; ?>%
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    <?php else: ?>
        <div class="bg-white shadow-lg rounded-lg">
            <div class="text-center py-12">
                <i class="fas fa-user-graduate text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Students Found</h3>
                <p class="text-gray-500">No students are enrolled in your classes yet.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
