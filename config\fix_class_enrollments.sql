-- Fix for class_enrollments table issue
-- This script will rename the existing enrollments table to class_enrollments
-- and update the column name to match what the application expects

USE attendance_system;

-- First, check if class_enrollments already exists
DROP TABLE IF EXISTS class_enrollments;

-- Rename enrollments table to class_enrollments
RENAME TABLE enrollments TO class_enrollments;

-- Add enrolled_at column as an alias for enrollment_date
ALTER TABLE class_enrollments
ADD COLUMN enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER class_id;

-- Copy data from enrollment_date to enrolled_at
UPDATE class_enrollments SET enrolled_at = enrollment_date;

-- Drop the old enrollment_date column
ALTER TABLE class_enrollments DROP COLUMN enrollment_date;

-- Add missing columns to classes table that the application expects
ALTER TABLE classes
ADD COLUMN IF NOT EXISTS schedule_day VARCHAR(20) AFTER schedule,
ADD COLUMN IF NOT EXISTS start_time TIME AFTER schedule_day,
ADD COLUMN IF NOT EXISTS end_time TIME AFTER start_time,
ADD COLUMN IF NOT EXISTS room_number VARCHAR(50) AFTER end_time;

-- Update the existing schedule column data to populate the new columns
-- This is a basic migration - you may need to adjust based on your existing data format
UPDATE classes
SET
    schedule_day = 'Monday',
    start_time = '09:00:00',
    end_time = '10:30:00',
    room_number = room
WHERE schedule_day IS NULL;

-- Insert some sample enrollments for testing (optional)
-- You can remove this section if you don't want sample data
INSERT IGNORE INTO class_enrollments (student_id, class_id, enrolled_at, status)
SELECT
    u.id as student_id,
    c.id as class_id,
    NOW() as enrolled_at,
    'active' as status
FROM users u
CROSS JOIN classes c
WHERE u.role = 'student'
LIMIT 10;

-- Display success message
SELECT 'class_enrollments table created successfully!' as message;
SELECT 'Database structure has been fixed.' as info;
