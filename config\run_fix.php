<?php
require_once 'database.php';

try {
    // Read the SQL file
    $sql = file_get_contents('fix_class_enrollments.sql');
    
    // Split the SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    echo "Starting database fix...\n";
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^--/', $statement) && !preg_match('/^SELECT.*message/', $statement)) {
            try {
                $db->exec($statement);
                echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
            } catch (PDOException $e) {
                echo "⚠ Warning: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\nDatabase fix completed!\n";
    echo "The class_enrollments table has been created.\n";
    
    // Verify the table exists
    $stmt = $db->query("SHOW TABLES LIKE 'class_enrollments'");
    if ($stmt->rowCount() > 0) {
        echo "✓ class_enrollments table exists\n";
        
        // Show table structure
        $stmt = $db->query("DESCRIBE class_enrollments");
        echo "\nTable structure:\n";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['Field']} ({$row['Type']})\n";
        }
    } else {
        echo "✗ class_enrollments table not found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
