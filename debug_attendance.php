<?php
session_start();
require_once 'config/database.php';

// Check if user is student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: auth/login.php');
    exit();
}

$student_id = $_SESSION['user_id'];
$class_filter = $_GET['class_id'] ?? '';
$month_filter = $_GET['month'] ?? date('Y-m');

echo "<!DOCTYPE html>
<html>
<head>
    <title>Debug Attendance</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { color: blue; }
        .error { color: red; }
        .success { color: green; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Debug Attendance Data</h1>";

echo "<div class='info'>Student ID: $student_id</div>";
echo "<div class='info'>Class Filter: " . ($class_filter ?: 'None') . "</div>";
echo "<div class='info'>Month Filter: $month_filter</div><br>";

try {
    // 1. Check if student has any enrollments
    echo "<h2>1. Student Enrollments</h2>";
    $stmt = $db->prepare("SELECT * FROM enrollments WHERE student_id = ?");
    $stmt->execute([$student_id]);
    $enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($enrollments)) {
        echo "<div class='error'>❌ No enrollments found for this student!</div>";
        echo "<div class='info'>This student is not enrolled in any classes.</div>";
    } else {
        echo "<div class='success'>✅ Found " . count($enrollments) . " enrollments</div>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Class ID</th><th>Status</th><th>Enrolled At</th></tr>";
        foreach ($enrollments as $enrollment) {
            echo "<tr>";
            echo "<td>{$enrollment['id']}</td>";
            echo "<td>{$enrollment['class_id']}</td>";
            echo "<td>{$enrollment['status']}</td>";
            echo "<td>{$enrollment['enrolled_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 2. Check classes the student is enrolled in
    echo "<h2>2. Student's Classes</h2>";
    $stmt = $db->prepare("
        SELECT cl.id, c.name as course_name, c.code as course_code, 
               cl.section, u.full_name as teacher_name
        FROM enrollments e
        JOIN classes cl ON e.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        JOIN users u ON cl.teacher_id = u.id
        WHERE e.student_id = ? AND e.status = 'active'
        ORDER BY c.name, cl.section
    ");
    $stmt->execute([$student_id]);
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($classes)) {
        echo "<div class='error'>❌ No active classes found for this student!</div>";
    } else {
        echo "<div class='success'>✅ Found " . count($classes) . " active classes</div>";
        echo "<table>";
        echo "<tr><th>Class ID</th><th>Course</th><th>Code</th><th>Section</th><th>Teacher</th></tr>";
        foreach ($classes as $class) {
            echo "<tr>";
            echo "<td>{$class['id']}</td>";
            echo "<td>{$class['course_name']}</td>";
            echo "<td>{$class['code']}</td>";
            echo "<td>{$class['section']}</td>";
            echo "<td>{$class['teacher_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 3. Check if the specific class exists (if filter is applied)
    if ($class_filter) {
        echo "<h2>3. Specific Class Check (ID: $class_filter)</h2>";
        $stmt = $db->prepare("
            SELECT cl.*, c.name as course_name, c.code as course_code
            FROM classes cl
            JOIN courses c ON cl.course_id = c.id
            WHERE cl.id = ?
        ");
        $stmt->execute([$class_filter]);
        $specific_class = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$specific_class) {
            echo "<div class='error'>❌ Class ID $class_filter does not exist!</div>";
        } else {
            echo "<div class='success'>✅ Class exists: {$specific_class['course_name']} - {$specific_class['code']}</div>";
            
            // Check if student is enrolled in this specific class
            $stmt = $db->prepare("SELECT * FROM enrollments WHERE student_id = ? AND class_id = ?");
            $stmt->execute([$student_id, $class_filter]);
            $enrollment = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$enrollment) {
                echo "<div class='error'>❌ Student is NOT enrolled in this class!</div>";
            } else {
                echo "<div class='success'>✅ Student is enrolled in this class</div>";
            }
        }
    }
    
    // 4. Check attendance sessions
    echo "<h2>4. Attendance Sessions</h2>";
    $where_conditions = ["e.student_id = ?"];
    $params = [$student_id];
    
    if ($class_filter) {
        $where_conditions[] = "cl.id = ?";
        $params[] = $class_filter;
    }
    
    if ($month_filter) {
        $where_conditions[] = "DATE_FORMAT(ats.date, '%Y-%m') = ?";
        $params[] = $month_filter;
    }
    
    $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
    
    $query = "
        SELECT 
            ats.id as session_id,
            ats.date,
            ats.start_time,
            ats.end_time,
            ats.topic,
            c.name as course_name,
            c.code as course_code,
            cl.section,
            cl.id as class_id
        FROM enrollments e
        JOIN classes cl ON e.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        JOIN attendance_sessions ats ON cl.id = ats.class_id
        $where_clause
        ORDER BY ats.date DESC, ats.start_time DESC
    ";
    
    echo "<div class='info'>Query: " . str_replace('?', '%s', $query) . "</div>";
    echo "<div class='info'>Parameters: " . implode(', ', $params) . "</div><br>";
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($sessions)) {
        echo "<div class='error'>❌ No attendance sessions found for the given criteria!</div>";
        
        // Check if there are any sessions at all for this student
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM enrollments e
            JOIN classes cl ON e.class_id = cl.id
            JOIN attendance_sessions ats ON cl.id = ats.class_id
            WHERE e.student_id = ?
        ");
        $stmt->execute([$student_id]);
        $total_sessions = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "<div class='info'>Total sessions for this student (all time): $total_sessions</div>";
        
        if ($total_sessions == 0) {
            echo "<div class='error'>No attendance sessions have been created for any of this student's classes!</div>";
        }
    } else {
        echo "<div class='success'>✅ Found " . count($sessions) . " attendance sessions</div>";
        echo "<table>";
        echo "<tr><th>Session ID</th><th>Date</th><th>Time</th><th>Course</th><th>Topic</th></tr>";
        foreach ($sessions as $session) {
            echo "<tr>";
            echo "<td>{$session['session_id']}</td>";
            echo "<td>{$session['date']}</td>";
            echo "<td>{$session['start_time']} - {$session['end_time']}</td>";
            echo "<td>{$session['course_name']} ({$session['course_code']})</td>";
            echo "<td>{$session['topic']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. Check attendance records
    if (!empty($sessions)) {
        echo "<h2>5. Attendance Records</h2>";
        $session_ids = array_column($sessions, 'session_id');
        $placeholders = str_repeat('?,', count($session_ids) - 1) . '?';
        
        $stmt = $db->prepare("
            SELECT ar.*, ats.date, ats.topic
            FROM attendance_records ar
            JOIN attendance_sessions ats ON ar.session_id = ats.id
            WHERE ar.student_id = ? AND ar.session_id IN ($placeholders)
            ORDER BY ats.date DESC
        ");
        $stmt->execute(array_merge([$student_id], $session_ids));
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($records)) {
            echo "<div class='error'>❌ No attendance records found! Attendance has not been marked for this student.</div>";
        } else {
            echo "<div class='success'>✅ Found " . count($records) . " attendance records</div>";
            echo "<table>";
            echo "<tr><th>Session ID</th><th>Date</th><th>Status</th><th>Marked At</th><th>Remarks</th></tr>";
            foreach ($records as $record) {
                echo "<tr>";
                echo "<td>{$record['session_id']}</td>";
                echo "<td>{$record['date']}</td>";
                echo "<td>{$record['status']}</td>";
                echo "<td>{$record['marked_at']}</td>";
                echo "<td>{$record['remarks']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
} catch (PDOException $e) {
    echo "<div class='error'>Database error: " . $e->getMessage() . "</div>";
}

echo "<br><div class='info'><a href='student/my-attendance.php'>← Back to My Attendance</a></div>";
echo "</body></html>";
?>
