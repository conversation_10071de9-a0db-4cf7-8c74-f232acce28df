<?php
require_once 'database.php';

try {
    echo "Testing database fix...\n\n";
    
    // Test 1: Check if class_enrollments table exists
    echo "1. Checking if class_enrollments table exists:\n";
    $stmt = $db->query("SHOW TABLES LIKE 'class_enrollments'");
    if ($stmt->rowCount() > 0) {
        echo "   ✓ class_enrollments table exists\n";
    } else {
        echo "   ✗ class_enrollments table NOT found\n";
        exit(1);
    }
    
    // Test 2: Check table structure
    echo "\n2. Checking table structure:\n";
    $stmt = $db->query("DESCRIBE class_enrollments");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($columns as $column) {
        echo "   - {$column['Field']} ({$column['Type']})\n";
    }
    
    // Test 3: Check if classes table has required columns
    echo "\n3. Checking classes table structure:\n";
    $stmt = $db->query("DESCRIBE classes");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $required_columns = ['schedule_day', 'start_time', 'end_time', 'room_number'];
    $found_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $col) {
        if (in_array($col, $found_columns)) {
            echo "   ✓ $col column exists\n";
        } else {
            echo "   ✗ $col column missing\n";
        }
    }
    
    // Test 4: Try a sample query that was failing
    echo "\n4. Testing sample query:\n";
    $stmt = $db->prepare("
        SELECT COUNT(*) as count
        FROM class_enrollments ce
        JOIN classes c ON ce.class_id = c.id
        LIMIT 1
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "   ✓ Query executed successfully. Found {$result['count']} enrollments.\n";
    
    // Test 5: Check if we have any users and classes for testing
    echo "\n5. Checking test data:\n";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE role = 'student'");
    $students = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "   Students: $students\n";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM classes");
    $classes = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "   Classes: $classes\n";
    
    $stmt = $db->query("SELECT COUNT(*) as count FROM class_enrollments");
    $enrollments = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "   Enrollments: $enrollments\n";
    
    echo "\n✓ All tests passed! The database fix was successful.\n";
    
} catch (PDOException $e) {
    echo "✗ Database error: " . $e->getMessage() . "\n";
    exit(1);
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
    exit(1);
}
?>
