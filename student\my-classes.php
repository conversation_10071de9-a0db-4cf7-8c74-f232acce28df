<?php
session_start();
require_once '../config/database.php';

// Check if user is logged in and is a student
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'student') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'My Classes';
$student_id = $_SESSION['user_id'];

// Get student's enrolled classes
try {
    $stmt = $db->prepare("
        SELECT 
            c.id as class_id,
            c.section,
            c.schedule_day,
            c.start_time,
            c.end_time,
            c.room_number,
            co.id as course_id,
            co.name as course_name,
            co.code as course_code,
            co.credits,
            co.description as course_description,
            u.full_name as teacher_name,
            u.email as teacher_email,
            d.name as department_name,
            d.code as department_code,
            ce.enrolled_at
        FROM enrollments ce
        JOIN classes c ON ce.class_id = c.id
        JOIN courses co ON c.course_id = co.id
        JOIN users u ON c.teacher_id = u.id
        JOIN departments d ON co.department_id = d.id
        WHERE ce.student_id = ?
        ORDER BY co.name, c.section
    ");
    $stmt->execute([$student_id]);
    $classes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get attendance statistics for each class
    $attendance_stats = [];
    foreach ($classes as $class) {
        $stmt = $db->prepare("
            SELECT 
                COUNT(DISTINCT ats.id) as total_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'present' THEN ats.id END) as present_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'absent' THEN ats.id END) as absent_sessions,
                COUNT(DISTINCT CASE WHEN ar.status = 'late' THEN ats.id END) as late_sessions
            FROM attendance_sessions ats
            LEFT JOIN attendance_records ar ON ats.id = ar.session_id AND ar.student_id = ?
            WHERE ats.class_id = ?
        ");
        $stmt->execute([$student_id, $class['class_id']]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $attendance_percentage = $stats['total_sessions'] > 0 
            ? round(($stats['present_sessions'] / $stats['total_sessions']) * 100, 2)
            : 0;
            
        $attendance_stats[$class['class_id']] = array_merge($stats, [
            'attendance_percentage' => $attendance_percentage
        ]);
    }

    // Get total credits
    $total_credits = array_sum(array_column($classes, 'credits'));

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <i class="fas fa-chalkboard mr-3 text-indigo-600"></i>
                    My Classes
                </h1>
                <p class="mt-2 text-gray-600">View your enrolled courses and class information</p>
            </div>
            <a href="dashboard.php" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <?php if (isset($error)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-book text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Classes</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo count($classes); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-graduation-cap text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Credits</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_credits; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-percentage text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Avg Attendance</p>
                        <?php 
                        $total_sessions = array_sum(array_column($attendance_stats, 'total_sessions'));
                        $total_present = array_sum(array_column($attendance_stats, 'present_sessions'));
                        $avg_attendance = $total_sessions > 0 ? round(($total_present / $total_sessions) * 100, 1) : 0;
                        ?>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $avg_attendance; ?>%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Classes List -->
    <?php if (!empty($classes)): ?>
        <div class="space-y-6">
            <?php foreach ($classes as $class): ?>
                <?php $stats = $attendance_stats[$class['class_id']]; ?>
                <div class="bg-white shadow-lg rounded-lg overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($class['course_name']); ?></h3>
                                <p class="text-sm text-gray-600"><?php echo htmlspecialchars($class['course_code']); ?> - Section <?php echo htmlspecialchars($class['section']); ?></p>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                    <?php echo $class['credits']; ?> Credits
                                </span>
                                <?php 
                                $percentage = $stats['attendance_percentage'];
                                $color_class = $percentage >= 75 ? 'bg-green-100 text-green-800' : ($percentage >= 50 ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800');
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $color_class; ?>">
                                    <?php echo $percentage; ?>% Attendance
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Class Information -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Class Information</h4>
                                <div class="space-y-2 text-sm text-gray-600">
                                    <div class="flex items-center">
                                        <i class="fas fa-building w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Department:</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($class['department_name']); ?> (<?php echo htmlspecialchars($class['department_code']); ?>)</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-user w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Instructor:</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($class['teacher_name']); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-envelope w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Email:</span>
                                        <a href="mailto:<?php echo htmlspecialchars($class['teacher_email']); ?>" class="ml-2 text-indigo-600 hover:text-indigo-800">
                                            <?php echo htmlspecialchars($class['teacher_email']); ?>
                                        </a>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Schedule:</span>
                                        <span class="ml-2">
                                            <?php echo htmlspecialchars($class['schedule_day']); ?> 
                                            <?php echo date('g:i A', strtotime($class['start_time'])) . ' - ' . date('g:i A', strtotime($class['end_time'])); ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-map-marker-alt w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Room:</span>
                                        <span class="ml-2"><?php echo htmlspecialchars($class['room_number'] ?: 'TBA'); ?></span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar-plus w-4 mr-3 text-gray-400"></i>
                                        <span class="font-medium">Enrolled:</span>
                                        <span class="ml-2"><?php echo date('M d, Y', strtotime($class['enrolled_at'])); ?></span>
                                    </div>
                                </div>
                                
                                <?php if (!empty($class['course_description'])): ?>
                                    <div class="mt-4">
                                        <h5 class="text-sm font-semibold text-gray-900 mb-2">Course Description</h5>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($class['course_description']); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <!-- Attendance Summary -->
                            <div>
                                <h4 class="text-sm font-semibold text-gray-900 mb-3">Attendance Summary</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Total Sessions:</span>
                                        <span class="text-sm font-medium text-gray-900"><?php echo $stats['total_sessions']; ?></span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Present:</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <?php echo $stats['present_sessions']; ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Absent:</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <?php echo $stats['absent_sessions']; ?>
                                        </span>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">Late:</span>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                            <?php echo $stats['late_sessions']; ?>
                                        </span>
                                    </div>
                                    <div class="pt-2 border-t border-gray-200">
                                        <div class="flex items-center justify-between">
                                            <span class="text-sm font-medium text-gray-900">Attendance Rate:</span>
                                            <span class="text-sm font-bold <?php echo $percentage >= 75 ? 'text-green-600' : ($percentage >= 50 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                                <?php echo $percentage; ?>%
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="bg-white shadow-lg rounded-lg">
            <div class="text-center py-12">
                <i class="fas fa-book-open text-gray-400 text-4xl mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Classes Enrolled</h3>
                <p class="text-gray-500">You are not currently enrolled in any classes. Please contact your academic advisor for assistance.</p>
            </div>
        </div>
    <?php endif; ?>
</div>

<?php include '../includes/footer.php'; ?>
