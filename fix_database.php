<?php
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Database Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Database Fix for class_enrollments Table</h1>";

try {
    echo "<div class='info'>Starting database fix...</div><br>";
    
    // Check if enrollments table exists
    $stmt = $db->query("SHOW TABLES LIKE 'enrollments'");
    if ($stmt->rowCount() == 0) {
        echo "<div class='error'>Error: enrollments table does not exist!</div>";
        exit;
    }
    
    // Check if class_enrollments already exists
    $stmt = $db->query("SHOW TABLES LIKE 'class_enrollments'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='success'>class_enrollments table already exists!</div>";
    } else {
        echo "<div class='info'>Renaming enrollments table to class_enrollments...</div><br>";
        
        // Rename enrollments table to class_enrollments
        $db->exec("RENAME TABLE enrollments TO class_enrollments");
        echo "<div class='success'>✓ Table renamed successfully</div><br>";
        
        // Check if enrolled_at column exists
        $stmt = $db->query("SHOW COLUMNS FROM class_enrollments LIKE 'enrolled_at'");
        if ($stmt->rowCount() == 0) {
            echo "<div class='info'>Adding enrolled_at column...</div><br>";
            
            // Add enrolled_at column
            $db->exec("ALTER TABLE class_enrollments ADD COLUMN enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP AFTER class_id");
            echo "<div class='success'>✓ enrolled_at column added</div><br>";
            
            // Copy data from enrollment_date to enrolled_at
            $db->exec("UPDATE class_enrollments SET enrolled_at = enrollment_date");
            echo "<div class='success'>✓ Data copied to enrolled_at column</div><br>";
            
            // Drop the old enrollment_date column
            $db->exec("ALTER TABLE class_enrollments DROP COLUMN enrollment_date");
            echo "<div class='success'>✓ enrollment_date column removed</div><br>";
        }
    }
    
    // Add missing columns to classes table
    echo "<div class='info'>Checking classes table columns...</div><br>";
    
    $columns_to_add = [
        'schedule_day' => "VARCHAR(20) AFTER schedule",
        'start_time' => "TIME AFTER schedule_day", 
        'end_time' => "TIME AFTER start_time",
        'room_number' => "VARCHAR(50) AFTER end_time"
    ];
    
    foreach ($columns_to_add as $column => $definition) {
        $stmt = $db->query("SHOW COLUMNS FROM classes LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            $db->exec("ALTER TABLE classes ADD COLUMN $column $definition");
            echo "<div class='success'>✓ Added $column column to classes table</div><br>";
        } else {
            echo "<div class='info'>$column column already exists in classes table</div><br>";
        }
    }
    
    // Update existing classes with default values
    echo "<div class='info'>Setting default values for new columns...</div><br>";
    $db->exec("UPDATE classes SET 
        schedule_day = 'Monday',
        start_time = '09:00:00',
        end_time = '10:30:00',
        room_number = COALESCE(room, 'TBA')
        WHERE schedule_day IS NULL");
    echo "<div class='success'>✓ Default values set</div><br>";
    
    // Verify the fix
    echo "<div class='info'>Verifying the fix...</div><br>";
    
    $stmt = $db->query("SHOW TABLES LIKE 'class_enrollments'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='success'>✓ class_enrollments table exists</div><br>";
        
        // Show table structure
        $stmt = $db->query("DESCRIBE class_enrollments");
        echo "<div class='info'>Table structure:</div>";
        echo "<ul>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<li>{$row['Field']} ({$row['Type']})</li>";
        }
        echo "</ul>";
        
        // Count records
        $stmt = $db->query("SELECT COUNT(*) as count FROM class_enrollments");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<div class='success'>✓ Table contains $count enrollment records</div><br>";
    } else {
        echo "<div class='error'>✗ class_enrollments table not found</div>";
    }
    
    echo "<div class='success'><h2>Database fix completed successfully!</h2></div>";
    echo "<div class='info'>You can now access <a href='student/my-classes.php'>student/my-classes.php</a> without errors.</div>";
    
} catch (PDOException $e) {
    echo "<div class='error'>Database error: " . $e->getMessage() . "</div>";
} catch (Exception $e) {
    echo "<div class='error'>Error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
